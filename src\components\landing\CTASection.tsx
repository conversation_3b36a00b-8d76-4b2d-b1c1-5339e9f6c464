import { motion } from "framer-motion";
import { <PERSON>R<PERSON>, CheckCircle } from "lucide-react";
import { useContext } from "react";

import ctaImage from '@/assets/images/Flux_Dev_A_highly_detailed_abstract_8K_wallpaper_A_mesmerizing_3.jpg';
import { But<PERSON> } from "@/components/ui/button";
import { AuthContext } from "@/contexts/AuthContext";

export const CTASection = () => {
  const { user, setShowChatbot } = useContext(AuthContext);
  
  const handleCTA = () => {
    if (user) {
      // Se logado, redireciona para dashboard
      window.location.href = '/dashboard';
    } else {
      // Se não logado, abre chatbot
      setShowChatbot?.(true);
    }
  };
  
  return (
    <section className="relative py-32">
      {/* Background com imagem */}
      <img
        src={ctaImage}
        alt="Background CTA"
        className="absolute inset-0 w-full h-full object-cover z-0"
        style={{ filter: "blur(1px) brightness(0.3)" }}
      />
      <div className="absolute inset-0 bg-black/70 z-10" />
      
      <div className="container mx-auto px-4 relative z-20">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="max-w-4xl mx-auto text-center"
        >
          {/* Headline Principal */}
          <h2 className="text-5xl md:text-6xl font-bold mb-6 text-white leading-tight">
            Reduza <span className="bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">30%</span> dos custos
          </h2>
          
          <p className="text-2xl text-slate-300 mb-12 max-w-3xl mx-auto">
            Teste grátis por 14 dias. Sem cartão de crédito.
          </p>
          
          {/* CTA Principal */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.6 }}
            viewport={{ once: true }}
            className="mb-8"
          >
            <Button 
              size="lg"
              onClick={handleCTA}
              className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-black font-bold text-xl px-12 py-6 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 transform hover:scale-105"
            >
              Começar Teste Gratuito
              <ArrowRight className="ml-2 h-6 w-6" />
            </Button>
          </motion.div>
          
          {/* Trust Badges Minimalistas */}
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            viewport={{ once: true }}
            className="flex flex-wrap justify-center gap-8 text-slate-300"
          >
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-400" />
              <span>14 dias grátis</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-400" />
              <span>Sem cartão</span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-400" />
              <span>Suporte incluído</span>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};
